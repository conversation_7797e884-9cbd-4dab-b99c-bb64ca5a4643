/**
 * PLRA E-Stamping and Registration System
 * Main JavaScript File
 * Version: 1.0
 */

// Global variables
let currentCategory = '';
let currentSubcategory = '';

// Category data structure
const categoryData = {
    property: {
        title: 'Property Related Stamps',
        icon: 'fas fa-home',
        subcategories: {
            'sale-deed': {
                title: 'Sale Deed',
                description: 'Registration of property sale transactions between buyer and seller.',
                stampDuty: '5% of property value',
                documents: ['Property documents', 'Identity proof', 'Address proof', 'NOC if applicable'],
                estimatedTime: '2-3 working days',
                fees: 'As per government rates'
            },
            'gift-deed': {
                title: 'Gift Deed',
                description: 'Registration of property transfer as a gift without monetary consideration.',
                stampDuty: '2% of property value',
                documents: ['Property documents', 'Identity proof', 'Relationship proof', 'Affidavit'],
                estimatedTime: '2-3 working days',
                fees: 'As per government rates'
            },
            'mortgage-deed': {
                title: 'Mortgage Deed',
                description: 'Registration of property mortgage for loan or security purposes.',
                stampDuty: '0.5% of loan amount',
                documents: ['Property documents', 'Loan agreement', 'Identity proof', 'Income proof'],
                estimatedTime: '3-4 working days',
                fees: 'As per government rates'
            },
            'lease-agreement': {
                title: 'Lease Agreement',
                description: 'Registration of property lease for rental or commercial purposes.',
                stampDuty: '2% of annual rent',
                documents: ['Property documents', 'Identity proof', 'Lease terms', 'Security deposit proof'],
                estimatedTime: '1-2 working days',
                fees: 'As per government rates'
            }
        }
    },
    business: {
        title: 'Business Contracts',
        icon: 'fas fa-briefcase',
        subcategories: {
            'partnership-deed': {
                title: 'Partnership Deed',
                description: 'Registration of business partnership agreements and terms.',
                stampDuty: 'Rs. 1000 fixed',
                documents: ['Partnership agreement', 'Identity proof', 'Address proof', 'Business plan'],
                estimatedTime: '2-3 working days',
                fees: 'As per government rates'
            },
            'service-agreement': {
                title: 'Service Agreement',
                description: 'Registration of service contracts between parties.',
                stampDuty: '0.1% of contract value',
                documents: ['Service contract', 'Identity proof', 'Company registration', 'Terms of service'],
                estimatedTime: '1-2 working days',
                fees: 'As per government rates'
            },
            'supply-contract': {
                title: 'Supply Contract',
                description: 'Registration of goods supply and delivery contracts.',
                stampDuty: '0.1% of contract value',
                documents: ['Supply agreement', 'Identity proof', 'Business license', 'Product specifications'],
                estimatedTime: '2-3 working days',
                fees: 'As per government rates'
            }
        }
    },
    legal: {
        title: 'Legal Documents',
        icon: 'fas fa-gavel',
        subcategories: {
            'affidavit': {
                title: 'Affidavit',
                description: 'Sworn statement of facts for legal purposes.',
                stampDuty: 'Rs. 20 fixed',
                documents: ['Identity proof', 'Address proof', 'Supporting documents'],
                estimatedTime: 'Same day',
                fees: 'As per government rates'
            },
            'power-of-attorney': {
                title: 'Power of Attorney',
                description: 'Legal authorization to act on behalf of another person.',
                stampDuty: 'Rs. 100 fixed',
                documents: ['Identity proof', 'Address proof', 'Witness documents'],
                estimatedTime: '1-2 working days',
                fees: 'As per government rates'
            },
            'indemnity-bond': {
                title: 'Indemnity Bond',
                description: 'Legal protection against loss or damage.',
                stampDuty: '0.5% of bond amount',
                documents: ['Identity proof', 'Financial documents', 'Surety details'],
                estimatedTime: '2-3 working days',
                fees: 'As per government rates'
            }
        }
    },
    revenue: {
        title: 'Revenue Stamps',
        icon: 'fas fa-receipt',
        subcategories: {
            'certificates': {
                title: 'Government Certificates',
                description: 'Various government issued certificates and documents.',
                stampDuty: 'As per certificate type',
                documents: ['Application form', 'Identity proof', 'Supporting documents'],
                estimatedTime: '3-5 working days',
                fees: 'As per government rates'
            },
            'licenses': {
                title: 'Business Licenses',
                description: 'Registration and renewal of business licenses.',
                stampDuty: 'As per license type',
                documents: ['Application form', 'Business documents', 'Identity proof'],
                estimatedTime: '5-7 working days',
                fees: 'As per government rates'
            }
        }
    },
    financial: {
        title: 'Financial Documents',
        icon: 'fas fa-chart-line',
        subcategories: {
            'loan-agreement': {
                title: 'Loan Agreement',
                description: 'Registration of loan agreements and terms.',
                stampDuty: '0.25% of loan amount',
                documents: ['Loan agreement', 'Identity proof', 'Income proof', 'Collateral documents'],
                estimatedTime: '3-4 working days',
                fees: 'As per government rates'
            },
            'insurance-policy': {
                title: 'Insurance Policy',
                description: 'Registration of insurance policies and claims.',
                stampDuty: 'Rs. 50 fixed',
                documents: ['Policy document', 'Identity proof', 'Premium receipts'],
                estimatedTime: '1-2 working days',
                fees: 'As per government rates'
            }
        }
    },
    other: {
        title: 'Other Services',
        icon: 'fas fa-ellipsis-h',
        subcategories: {
            'miscellaneous': {
                title: 'Miscellaneous Documents',
                description: 'Other documents not covered in specific categories.',
                stampDuty: 'As per document type',
                documents: ['Relevant documents', 'Identity proof', 'Supporting papers'],
                estimatedTime: '2-5 working days',
                fees: 'As per government rates'
            }
        }
    }
};

/**
 * Navigate to category page
 * @param {string} category - Category identifier
 */
function navigateToCategory(category) {
    // Store category in URL parameter
    window.location.href = `categories.html?category=${category}`;
}

/**
 * Get URL parameter value
 * @param {string} name - Parameter name
 * @returns {string} Parameter value
 */
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

/**
 * Initialize page based on current location
 */
function initializePage() {
    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.plra-card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s ease-out';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        }, index * 100);
    });
}

/**
 * Show loading spinner
 */
function showLoading() {
    return `
        <div class="text-center py-5">
            <div class="spinner-border text-plra-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading details...</p>
        </div>
    `;
}

/**
 * Show error message
 * @param {string} message - Error message
 */
function showError(message) {
    return `
        <div class="text-center py-5">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h4 class="text-warning">Error</h4>
            <p class="text-muted">${message}</p>
        </div>
    `;
}

/**
 * Format document list as HTML
 * @param {Array} documents - List of required documents
 * @returns {string} HTML string
 */
function formatDocumentList(documents) {
    return documents.map(doc => `<li class="mb-1"><i class="fas fa-check-circle text-success me-2"></i>${doc}</li>`).join('');
}

/**
 * Start workflow for selected subcategory
 * @param {string} subcategoryId - Subcategory identifier
 */
function startWorkflow(subcategoryId) {
    // Show confirmation dialog
    if (confirm('Are you ready to start the registration process? You will be guided through multiple steps.')) {
        // In a real application, this would navigate to the workflow
        alert(`Starting workflow for: ${categoryData[currentCategory].subcategories[subcategoryId].title}\n\nThis would typically redirect to a multi-step form process.`);
        
        // For demo purposes, you could redirect to a workflow page
        // window.location.href = `workflow.html?category=${currentCategory}&subcategory=${subcategoryId}`;
    }
}

// Initialize page when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    
    // Add smooth scrolling to anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// Export functions for use in other scripts
window.PLRA = {
    navigateToCategory,
    getUrlParameter,
    categoryData,
    showLoading,
    showError,
    formatDocumentList,
    startWorkflow
};
