/**
 * PLRA E-Stamping and Registration System
 * Main JavaScript File
 * Version: 1.0
 */

// Global variables
let currentCategory = '';
let currentSubcategory = '';

// Category data structure
const categoryData = {
    property: {
        title: 'Property Related Stamps',
        icon: 'fas fa-home',
        subcategories: {
            'agricultural-land-sale': {
                title: 'Agricultural Land Sale',
                description: 'Registration of agricultural land sale transactions including farmland, orchards, and agricultural plots.',
                stampDuty: '3% of property value (minimum Rs. 5,000)',
                documents: ['Revenue records (Khasra/Khatauni)', 'Mutation certificate', 'Identity proof of buyer/seller', 'Agricultural land conversion certificate (if applicable)', 'NOC from agriculture department'],
                estimatedTime: '3-5 working days',
                fees: 'Registration: Rs. 500 + Stamp duty'
            },
            'commercial-property-transfer': {
                title: 'Commercial Property Transfer',
                description: 'Registration of commercial property sales including shops, offices, warehouses, and industrial plots.',
                stampDuty: '6% of property value (minimum Rs. 10,000)',
                documents: ['Property title documents', 'Commercial license', 'Building plan approval', 'Identity proof', 'Tax clearance certificate', 'Fire safety certificate'],
                estimatedTime: '4-6 working days',
                fees: 'Registration: Rs. 1,000 + Stamp duty'
            },
            'residential-property-sale': {
                title: 'Residential Property Sale',
                description: 'Registration of residential property transactions including houses, apartments, and residential plots.',
                stampDuty: '5% of property value (minimum Rs. 3,000)',
                documents: ['Property documents', 'Building plan approval', 'Completion certificate', 'Identity proof', 'Address proof', 'Property tax receipts'],
                estimatedTime: '2-4 working days',
                fees: 'Registration: Rs. 750 + Stamp duty'
            },
            'property-partition': {
                title: 'Property Partition',
                description: 'Legal division of joint property among co-owners or family members.',
                stampDuty: '2% of divided property value',
                documents: ['Original property documents', 'Family tree/succession certificate', 'Identity proof of all parties', 'Consent of all co-owners', 'Survey settlement records'],
                estimatedTime: '5-7 working days',
                fees: 'Registration: Rs. 500 + Stamp duty'
            },
            'property-mortgage': {
                title: 'Property Mortgage',
                description: 'Registration of property mortgage for loan or security purposes with banks or financial institutions.',
                stampDuty: '0.5% of loan amount (minimum Rs. 1,000)',
                documents: ['Property documents', 'Loan sanction letter', 'Identity proof', 'Income proof', 'Bank NOC', 'Property valuation report'],
                estimatedTime: '3-4 working days',
                fees: 'Registration: Rs. 300 + Stamp duty'
            },
            'lease-deed-registration': {
                title: 'Lease Deed Registration',
                description: 'Registration of property lease agreements for rental or commercial purposes.',
                stampDuty: '2% of annual rent (minimum Rs. 500)',
                documents: ['Property ownership documents', 'Identity proof of lessor/lessee', 'Lease agreement terms', 'Security deposit proof', 'Address proof'],
                estimatedTime: '1-2 working days',
                fees: 'Registration: Rs. 200 + Stamp duty'
            },
            'gift-deed-registration': {
                title: 'Gift Deed Registration',
                description: 'Registration of property transfer as a gift without monetary consideration between family members.',
                stampDuty: '2% of property value (minimum Rs. 2,000)',
                documents: ['Property documents', 'Identity proof', 'Relationship proof', 'Gift deed affidavit', 'Witness statements', 'Property valuation certificate'],
                estimatedTime: '2-3 working days',
                fees: 'Registration: Rs. 400 + Stamp duty'
            },
            'property-exchange': {
                title: 'Property Exchange',
                description: 'Registration of property exchange transactions where two parties exchange their properties.',
                stampDuty: '5% of higher property value',
                documents: ['Property documents of both parties', 'Identity proof', 'Property valuation reports', 'Exchange agreement', 'Tax clearance certificates'],
                estimatedTime: '4-5 working days',
                fees: 'Registration: Rs. 800 + Stamp duty'
            }
        }
    },
    business: {
        title: 'Business Contracts',
        icon: 'fas fa-briefcase',
        subcategories: {
            'partnership-deed': {
                title: 'Partnership Deed',
                description: 'Registration of business partnership agreements defining roles, responsibilities, and profit sharing.',
                stampDuty: 'Rs. 1,000 fixed + Rs. 500 per partner',
                documents: ['Partnership agreement', 'Identity proof of all partners', 'Address proof', 'Business plan', 'Capital contribution proof', 'PAN cards'],
                estimatedTime: '2-3 working days',
                fees: 'Registration: Rs. 500 + Stamp duty'
            },
            'franchise-agreement': {
                title: 'Franchise Agreement',
                description: 'Registration of franchise agreements between franchisor and franchisee for business operations.',
                stampDuty: '0.2% of franchise fee (minimum Rs. 2,000)',
                documents: ['Franchise agreement', 'Franchisor registration certificate', 'Identity proof', 'Business license', 'Financial statements', 'Territory rights document'],
                estimatedTime: '3-4 working days',
                fees: 'Registration: Rs. 750 + Stamp duty'
            },
            'employment-contract': {
                title: 'Employment Contract',
                description: 'Registration of employment agreements for senior positions and executive contracts.',
                stampDuty: 'Rs. 100 fixed',
                documents: ['Employment contract', 'Identity proof of employer/employee', 'Company registration', 'Salary certificate', 'Job description', 'Educational certificates'],
                estimatedTime: '1-2 working days',
                fees: 'Registration: Rs. 200 + Stamp duty'
            },
            'non-disclosure-agreement': {
                title: 'Non-Disclosure Agreement',
                description: 'Registration of confidentiality agreements to protect sensitive business information.',
                stampDuty: 'Rs. 500 fixed',
                documents: ['NDA document', 'Identity proof of parties', 'Company registration', 'Business license', 'Confidentiality scope document'],
                estimatedTime: '1-2 working days',
                fees: 'Registration: Rs. 300 + Stamp duty'
            },
            'vendor-agreement': {
                title: 'Vendor Agreement',
                description: 'Registration of vendor and supplier agreements for goods and services procurement.',
                stampDuty: '0.1% of contract value (minimum Rs. 1,000)',
                documents: ['Vendor agreement', 'Vendor registration certificate', 'Identity proof', 'Business license', 'Tax registration', 'Quality certificates'],
                estimatedTime: '2-3 working days',
                fees: 'Registration: Rs. 400 + Stamp duty'
            },
            'distribution-agreement': {
                title: 'Distribution Agreement',
                description: 'Registration of distribution agreements for product sales and marketing rights.',
                stampDuty: '0.15% of agreement value (minimum Rs. 1,500)',
                documents: ['Distribution agreement', 'Distributor license', 'Identity proof', 'Territory mapping', 'Product catalog', 'Marketing plan'],
                estimatedTime: '3-4 working days',
                fees: 'Registration: Rs. 600 + Stamp duty'
            },
            'joint-venture-agreement': {
                title: 'Joint Venture Agreement',
                description: 'Registration of joint venture agreements between companies for collaborative projects.',
                stampDuty: '0.3% of project value (minimum Rs. 5,000)',
                documents: ['JV agreement', 'Company registration certificates', 'Identity proof of directors', 'Project proposal', 'Financial statements', 'Board resolutions'],
                estimatedTime: '4-5 working days',
                fees: 'Registration: Rs. 1,000 + Stamp duty'
            },
            'consultancy-agreement': {
                title: 'Consultancy Agreement',
                description: 'Registration of professional consultancy service agreements and contracts.',
                stampDuty: '0.1% of consultancy fee (minimum Rs. 500)',
                documents: ['Consultancy agreement', 'Professional license', 'Identity proof', 'Scope of work', 'Qualification certificates', 'Experience certificates'],
                estimatedTime: '2-3 working days',
                fees: 'Registration: Rs. 350 + Stamp duty'
            }
        }
    },
    legal: {
        title: 'Legal Documents',
        icon: 'fas fa-gavel',
        subcategories: {
            'general-affidavit': {
                title: 'General Affidavit',
                description: 'Sworn statement of facts for legal purposes including name change, address proof, and declarations.',
                stampDuty: 'Rs. 20 fixed',
                documents: ['Identity proof', 'Address proof', 'Supporting documents', 'Witness statements'],
                estimatedTime: 'Same day',
                fees: 'Registration: Rs. 50 + Stamp duty'
            },
            'income-affidavit': {
                title: 'Income Affidavit',
                description: 'Sworn statement declaring annual income for various government and legal purposes.',
                stampDuty: 'Rs. 50 fixed',
                documents: ['Identity proof', 'Income proof documents', 'Bank statements', 'Tax returns', 'Employment certificate'],
                estimatedTime: 'Same day',
                fees: 'Registration: Rs. 100 + Stamp duty'
            },
            'power-of-attorney-general': {
                title: 'General Power of Attorney',
                description: 'Legal authorization to act on behalf of another person for general matters.',
                stampDuty: 'Rs. 100 fixed',
                documents: ['Identity proof of both parties', 'Address proof', 'Witness documents', 'Relationship proof'],
                estimatedTime: '1-2 working days',
                fees: 'Registration: Rs. 200 + Stamp duty'
            },
            'power-of-attorney-property': {
                title: 'Property Power of Attorney',
                description: 'Specific authorization for property-related transactions and management.',
                stampDuty: 'Rs. 500 fixed',
                documents: ['Property documents', 'Identity proof', 'Address proof', 'Witness statements', 'Property valuation'],
                estimatedTime: '2-3 working days',
                fees: 'Registration: Rs. 300 + Stamp duty'
            },
            'indemnity-bond': {
                title: 'Indemnity Bond',
                description: 'Legal protection against loss or damage with financial guarantee.',
                stampDuty: '0.5% of bond amount (minimum Rs. 500)',
                documents: ['Identity proof', 'Financial documents', 'Surety details', 'Bank guarantee', 'Asset proof'],
                estimatedTime: '2-3 working days',
                fees: 'Registration: Rs. 250 + Stamp duty'
            },
            'agreement-to-sell': {
                title: 'Agreement to Sell',
                description: 'Legal agreement for future sale of property with terms and conditions.',
                stampDuty: '1% of property value (minimum Rs. 1,000)',
                documents: ['Property documents', 'Identity proof', 'Agreement terms', 'Advance payment proof', 'Property survey'],
                estimatedTime: '2-3 working days',
                fees: 'Registration: Rs. 400 + Stamp duty'
            },
            'will-testament': {
                title: 'Will and Testament',
                description: 'Legal document for distribution of assets after death.',
                stampDuty: 'Rs. 200 fixed',
                documents: ['Identity proof', 'Asset documents', 'Beneficiary details', 'Witness statements', 'Medical certificate'],
                estimatedTime: '3-4 working days',
                fees: 'Registration: Rs. 500 + Stamp duty'
            },
            'adoption-deed': {
                title: 'Adoption Deed',
                description: 'Legal document for child adoption with all legal formalities.',
                stampDuty: 'Rs. 100 fixed',
                documents: ['Identity proof of adoptive parents', 'Child birth certificate', 'Court order', 'Home study report', 'Medical certificates'],
                estimatedTime: '5-7 working days',
                fees: 'Registration: Rs. 300 + Stamp duty'
            }
        }
    },
    revenue: {
        title: 'Revenue Stamps',
        icon: 'fas fa-receipt',
        subcategories: {
            'birth-certificate': {
                title: 'Birth Certificate',
                description: 'Official government certificate of birth registration.',
                stampDuty: 'Rs. 10 fixed',
                documents: ['Hospital birth record', 'Identity proof of parents', 'Address proof', 'Immunization card'],
                estimatedTime: '3-5 working days',
                fees: 'Certificate: Rs. 50 + Stamp duty'
            },
            'death-certificate': {
                title: 'Death Certificate',
                description: 'Official government certificate of death registration.',
                stampDuty: 'Rs. 10 fixed',
                documents: ['Medical certificate', 'Identity proof of deceased', 'Identity proof of applicant', 'Relationship proof'],
                estimatedTime: '2-3 working days',
                fees: 'Certificate: Rs. 50 + Stamp duty'
            },
            'domicile-certificate': {
                title: 'Domicile Certificate',
                description: 'Certificate proving permanent residence in Punjab state.',
                stampDuty: 'Rs. 20 fixed',
                documents: ['Identity proof', 'Address proof', 'Residence proof for 3+ years', 'School/college certificates'],
                estimatedTime: '7-10 working days',
                fees: 'Certificate: Rs. 100 + Stamp duty'
            },
            'income-certificate': {
                title: 'Income Certificate',
                description: 'Official certificate stating annual family income.',
                stampDuty: 'Rs. 20 fixed',
                documents: ['Income proof', 'Identity proof', 'Bank statements', 'Employment certificate', 'Property documents'],
                estimatedTime: '5-7 working days',
                fees: 'Certificate: Rs. 75 + Stamp duty'
            },
            'caste-certificate': {
                title: 'Caste Certificate',
                description: 'Official certificate for caste verification and reservation benefits.',
                stampDuty: 'Rs. 15 fixed',
                documents: ['Identity proof', 'Caste proof documents', 'Family tree', 'Village records', 'School certificates'],
                estimatedTime: '10-15 working days',
                fees: 'Certificate: Rs. 100 + Stamp duty'
            },
            'business-license': {
                title: 'Business License',
                description: 'Registration and renewal of various business licenses.',
                stampDuty: 'Rs. 500 - Rs. 5,000 (as per business type)',
                documents: ['Business registration', 'Identity proof', 'Address proof', 'NOC from local authority', 'Fire safety certificate'],
                estimatedTime: '7-14 working days',
                fees: 'License fee: Rs. 1,000-10,000 + Stamp duty'
            }
        }
    },
    financial: {
        title: 'Financial Documents',
        icon: 'fas fa-chart-line',
        subcategories: {
            'personal-loan-agreement': {
                title: 'Personal Loan Agreement',
                description: 'Registration of personal loan agreements between individuals or with financial institutions.',
                stampDuty: '0.25% of loan amount (minimum Rs. 500)',
                documents: ['Loan agreement', 'Identity proof', 'Income proof', 'Bank statements', 'Guarantor documents'],
                estimatedTime: '2-3 working days',
                fees: 'Registration: Rs. 300 + Stamp duty'
            },
            'business-loan-agreement': {
                title: 'Business Loan Agreement',
                description: 'Registration of business loan agreements for commercial purposes.',
                stampDuty: '0.5% of loan amount (minimum Rs. 1,000)',
                documents: ['Loan agreement', 'Business registration', 'Financial statements', 'Collateral documents', 'Project report'],
                estimatedTime: '3-5 working days',
                fees: 'Registration: Rs. 500 + Stamp duty'
            },
            'insurance-policy-registration': {
                title: 'Insurance Policy Registration',
                description: 'Registration of life, health, and general insurance policies.',
                stampDuty: 'Rs. 50 - Rs. 200 (as per policy type)',
                documents: ['Policy document', 'Identity proof', 'Premium receipts', 'Medical reports (if applicable)', 'Nominee details'],
                estimatedTime: '1-2 working days',
                fees: 'Registration: Rs. 100 + Stamp duty'
            },
            'bank-guarantee': {
                title: 'Bank Guarantee',
                description: 'Registration of bank guarantees for various commercial and legal purposes.',
                stampDuty: '0.1% of guarantee amount (minimum Rs. 500)',
                documents: ['Bank guarantee document', 'Identity proof', 'Business registration', 'Financial statements', 'Collateral proof'],
                estimatedTime: '2-3 working days',
                fees: 'Registration: Rs. 400 + Stamp duty'
            },
            'fixed-deposit-receipt': {
                title: 'Fixed Deposit Receipt',
                description: 'Registration of fixed deposit receipts and investment certificates.',
                stampDuty: 'Rs. 100 fixed',
                documents: ['FD receipt', 'Identity proof', 'Bank account details', 'Investment proof', 'Nominee details'],
                estimatedTime: '1-2 working days',
                fees: 'Registration: Rs. 150 + Stamp duty'
            },
            'share-transfer-deed': {
                title: 'Share Transfer Deed',
                description: 'Registration of share transfer agreements and equity transactions.',
                stampDuty: '0.25% of share value (minimum Rs. 1,000)',
                documents: ['Share certificates', 'Transfer deed', 'Identity proof', 'Company registration', 'Board resolution'],
                estimatedTime: '3-4 working days',
                fees: 'Registration: Rs. 600 + Stamp duty'
            }
        }
    },
    other: {
        title: 'Other Services',
        icon: 'fas fa-ellipsis-h',
        subcategories: {
            'name-change-deed': {
                title: 'Name Change Deed',
                description: 'Legal document for official name change with government recognition.',
                stampDuty: 'Rs. 100 fixed',
                documents: ['Identity proof', 'Address proof', 'Affidavit for name change', 'Newspaper publication', 'Gazette notification'],
                estimatedTime: '5-7 working days',
                fees: 'Registration: Rs. 300 + Stamp duty'
            },
            'succession-certificate': {
                title: 'Succession Certificate',
                description: 'Legal certificate for inheritance of assets and property succession.',
                stampDuty: '1% of asset value (minimum Rs. 1,000)',
                documents: ['Death certificate', 'Identity proof of heirs', 'Asset documents', 'Family tree', 'Legal heir certificate'],
                estimatedTime: '10-15 working days',
                fees: 'Registration: Rs. 1,000 + Stamp duty'
            },
            'trademark-registration': {
                title: 'Trademark Registration',
                description: 'Registration of business trademarks and intellectual property rights.',
                stampDuty: 'Rs. 500 fixed',
                documents: ['Trademark application', 'Business registration', 'Logo/design samples', 'Identity proof', 'Usage proof'],
                estimatedTime: '15-30 working days',
                fees: 'Registration: Rs. 2,000 + Stamp duty'
            },
            'copyright-registration': {
                title: 'Copyright Registration',
                description: 'Registration of copyrights for literary, artistic, and creative works.',
                stampDuty: 'Rs. 200 fixed',
                documents: ['Copyright application', 'Original work samples', 'Identity proof', 'Creation proof', 'Publication details'],
                estimatedTime: '10-20 working days',
                fees: 'Registration: Rs. 1,500 + Stamp duty'
            },
            'arbitration-agreement': {
                title: 'Arbitration Agreement',
                description: 'Registration of arbitration agreements for dispute resolution.',
                stampDuty: 'Rs. 1,000 fixed',
                documents: ['Arbitration agreement', 'Identity proof of parties', 'Dispute details', 'Arbitrator details', 'Legal representation'],
                estimatedTime: '3-5 working days',
                fees: 'Registration: Rs. 750 + Stamp duty'
            },
            'court-fee-stamps': {
                title: 'Court Fee Stamps',
                description: 'Various court fee stamps for legal proceedings and judicial documents.',
                stampDuty: 'As per court requirements',
                documents: ['Court application', 'Case details', 'Identity proof', 'Legal documents', 'Lawyer authorization'],
                estimatedTime: 'Same day',
                fees: 'As per court fee schedule + Stamp duty'
            }
        }
    }
};

/**
 * Navigate to category page
 * @param {string} category - Category identifier
 */
function navigateToCategory(category) {
    // Store category in URL parameter
    window.location.href = `categories.html?category=${category}`;
}

/**
 * Get URL parameter value
 * @param {string} name - Parameter name
 * @returns {string} Parameter value
 */
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

/**
 * Initialize page based on current location
 */
function initializePage() {
    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.plra-card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s ease-out';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        }, index * 100);
    });
}

/**
 * Show loading spinner
 */
function showLoading() {
    return `
        <div class="text-center py-5">
            <div class="spinner-border text-plra-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading details...</p>
        </div>
    `;
}

/**
 * Show error message
 * @param {string} message - Error message
 */
function showError(message) {
    return `
        <div class="text-center py-5">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h4 class="text-warning">Error</h4>
            <p class="text-muted">${message}</p>
        </div>
    `;
}

/**
 * Format document list as HTML
 * @param {Array} documents - List of required documents
 * @returns {string} HTML string
 */
function formatDocumentList(documents) {
    return documents.map(doc => `<li class="mb-1"><i class="fas fa-check-circle text-success me-2"></i>${doc}</li>`).join('');
}

/**
 * Start workflow for selected subcategory
 * @param {string} subcategoryId - Subcategory identifier
 */
function startWorkflow(subcategoryId) {
    // Show confirmation dialog
    if (confirm('Are you ready to start the registration process? You will be guided through multiple steps.')) {
        // In a real application, this would navigate to the workflow
        alert(`Starting workflow for: ${categoryData[currentCategory].subcategories[subcategoryId].title}\n\nThis would typically redirect to a multi-step form process.`);
        
        // For demo purposes, you could redirect to a workflow page
        // window.location.href = `workflow.html?category=${currentCategory}&subcategory=${subcategoryId}`;
    }
}

// Initialize page when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    
    // Add smooth scrolling to anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// Export functions for use in other scripts
window.PLRA = {
    navigateToCategory,
    getUrlParameter,
    categoryData,
    showLoading,
    showError,
    formatDocumentList,
    startWorkflow
};
