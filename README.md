# PLRA E-Stamping and Registration System

A modern web-based user interface for the Punjab Land Revenue Authority's E-Stamping and Registration services in Pakistan.

## Project Structure

```
PLRA.ESORS.UI/
├── index.html                 # Landing page with main categories
├── categories.html            # Category selection with 2-column layout
├── assets/
│   ├── css/
│   │   ├── theme.css         # Centralized theme and styling
│   │   └── custom.css        # Additional custom styles
│   ├── js/
│   │   ├── main.js           # Main JavaScript functionality
│   │   └── categories.js     # Categories page specific JS
│   └── images/               # Images and assets (to be added)
└── README.md                 # This file
```

## Features

### Landing Page (index.html)
- Clean, professional government-style design
- Six main service categories:
  - Property Related Stamps
  - Business Contracts
  - Legal Documents
  - Revenue Stamps
  - Financial Documents
  - Other Services
- Responsive Bootstrap 5 layout
- Animated card interactions
- Information section with service hours and support details

### Categories Page (categories.html)
- Two-column layout as requested:
  - Left sidebar: Sub-category selection
  - Right panel: Detailed information and start button
- Breadcrumb navigation
- Dynamic content loading
- Process overview with step indicators
- Document requirements listing
- Download functionality for service information

### Centralized Theming
- **theme.css**: Complete centralized styling system
  - CSS custom properties (variables) for consistent theming
  - Punjab government color scheme (green and orange)
  - Reusable component styles
  - Responsive design utilities
  - Professional typography system
- **custom.css**: Additional project-specific styles

## Technology Stack

- **HTML5**: Semantic markup
- **Bootstrap 5**: Responsive framework
- **CSS3**: Custom properties and modern styling
- **JavaScript (ES6+)**: Interactive functionality
- **Font Awesome**: Icons and visual elements

## Color Scheme

The design uses Punjab government-inspired colors:
- **Primary Greenish Teal**: #059669 (Greenish Teal)
- **Primary Light**: #10B981 (Light Green)
- **Secondary Amber**: #F59E0B (Professional Amber)
- **Neutral Grays**: Various shades for text and backgrounds

## Getting Started

1. **Open the project**: Simply open `index.html` in a web browser
2. **Navigate**: Click on any category card to go to the categories page
3. **Select subcategory**: Choose from the left sidebar to see details
4. **Start process**: Click the "Start Registration Process" button

## Customization

### Adding New Categories
1. Edit the `categoryData` object in `assets/js/main.js`
2. Add new category with subcategories, descriptions, and requirements
3. Update the landing page cards in `index.html` if needed

### Modifying Styles
1. **Global changes**: Edit CSS variables in `assets/css/theme.css`
2. **Component styles**: Modify existing classes in `theme.css`
3. **Additional styles**: Add new styles to `assets/css/custom.css`

### Adding New Pages
1. Create new HTML file following the existing structure
2. Include the same CSS and JS files
3. Use existing CSS classes for consistency

## Browser Support

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Future Enhancements

- Multi-step workflow pages
- User authentication system
- Payment integration
- Document upload functionality
- Progress tracking
- Email notifications
- Mobile app integration

## Development Notes

- All styling is centralized in `theme.css` to avoid duplication
- JavaScript is modular and reusable
- Responsive design works on all device sizes
- Accessibility considerations included
- Print-friendly styles available

## Support

For technical support or questions about this UI implementation, please refer to the development team or create an issue in the project repository.