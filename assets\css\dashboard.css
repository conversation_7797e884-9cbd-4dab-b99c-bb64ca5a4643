/* 
 * PLRA E-Stamping and Registration System
 * Dashboard Specific CSS
 * Version: 1.0
 */

/* ===== DASHBOARD LAYOUT ===== */
.dashboard-body {
    background-color: var(--light-gray);
    font-family: var(--font-family-primary);
}

.dashboard-header {
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-md);
}

.dashboard-container {
    padding: 0;
    min-height: calc(100vh - 80px);
}

.dashboard-sidebar {
    background: var(--white);
    border-right: 1px solid var(--medium-gray);
    padding: 0;
    min-height: calc(100vh - 80px);
}

.sidebar-content {
    padding: var(--spacing-lg);
}

.sidebar-title {
    color: var(--primary-dark);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-light);
}

.dashboard-main {
    padding: var(--spacing-xl);
    background-color: var(--light-gray);
}

/* ===== NAVIGATION ===== */
.nav-pills .nav-link {
    color: var(--text-dark);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.nav-pills .nav-link:hover {
    background-color: var(--light-gray);
    color: var(--primary-color);
}

.nav-pills .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
}

.nav-pills .nav-link .badge {
    font-size: 0.7rem;
}

/* ===== DASHBOARD SECTIONS ===== */
.dashboard-section {
    display: none;
}

.dashboard-section.active {
    display: block;
}

.dashboard-title {
    color: var(--primary-dark);
    font-weight: 700;
    margin-bottom: 0;
}

.dashboard-date {
    color: var(--text-light);
    font-size: var(--font-size-sm);
}

/* ===== STATISTICS CARDS ===== */
.stat-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: none;
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.stat-card-primary .stat-icon {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
}

.stat-card-warning .stat-icon {
    background: linear-gradient(135deg, var(--warning) 0%, var(--secondary-dark) 100%);
}

.stat-card-success .stat-icon {
    background: linear-gradient(135deg, var(--success) 0%, var(--primary-light) 100%);
}

.stat-card-info .stat-icon {
    background: linear-gradient(135deg, var(--info) 0%, var(--primary-color) 100%);
}

.stat-content h3 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-dark);
    margin: 0;
}

.stat-content p {
    color: var(--text-light);
    margin: 0;
    font-size: var(--font-size-sm);
}

/* ===== DASHBOARD CARDS ===== */
.dashboard-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: none;
    margin-bottom: var(--spacing-xl);
}

.dashboard-card .card-header {
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
    color: var(--white);
    border: none;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.dashboard-card .card-header h5 {
    margin: 0;
    font-weight: 600;
}

.dashboard-card .card-body {
    padding: var(--spacing-xl);
}

/* ===== TABLES ===== */
.table {
    margin-bottom: 0;
}

.table th {
    background-color: var(--light-gray);
    color: var(--text-dark);
    font-weight: 600;
    border: none;
    padding: var(--spacing-md);
}

.table td {
    padding: var(--spacing-md);
    vertical-align: middle;
    border-color: var(--medium-gray);
}

.table-hover tbody tr:hover {
    background-color: rgba(5, 150, 105, 0.05);
}

/* ===== STATUS BADGES ===== */
.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning);
    border: 1px solid var(--warning);
}

.status-in-review {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info);
    border: 1px solid var(--info);
}

.status-verified {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
    border: 1px solid var(--success);
}

.status-rejected {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--error);
    border: 1px solid var(--error);
}

/* ===== CASE CARDS ===== */
.case-card {
    background: var(--white);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--medium-gray);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    transition: all 0.3s ease;
}

.case-card:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-light);
}

.case-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.case-id {
    font-weight: 700;
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.case-priority {
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.priority-high {
    background-color: var(--error);
    color: var(--white);
}

.priority-medium {
    background-color: var(--warning);
    color: var(--white);
}

.priority-low {
    background-color: var(--success);
    color: var(--white);
}

.case-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.case-info-item {
    display: flex;
    flex-direction: column;
}

.case-info-label {
    font-size: var(--font-size-xs);
    color: var(--text-light);
    text-transform: uppercase;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.case-info-value {
    color: var(--text-dark);
    font-weight: 500;
}

.case-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

/* ===== BUTTONS ===== */
.btn-action {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
    border-radius: var(--border-radius-md);
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-view {
    background-color: var(--info);
    border-color: var(--info);
    color: var(--white);
}

.btn-view:hover {
    background-color: #2563EB;
    border-color: #2563EB;
    color: var(--white);
}

.btn-approve {
    background-color: var(--success);
    border-color: var(--success);
    color: var(--white);
}

.btn-approve:hover {
    background-color: #059669;
    border-color: #059669;
    color: var(--white);
}

.btn-reject {
    background-color: var(--error);
    border-color: var(--error);
    color: var(--white);
}

.btn-reject:hover {
    background-color: #DC2626;
    border-color: #DC2626;
    color: var(--white);
}

.btn-forward {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white);
}

.btn-forward:hover {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
    color: var(--white);
}

/* ===== FILTERS ===== */
.dashboard-filters .form-control,
.dashboard-filters .form-select {
    border-color: var(--medium-gray);
    border-radius: var(--border-radius-md);
}

.dashboard-filters .form-control:focus,
.dashboard-filters .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(5, 150, 105, 0.25);
}

/* ===== MODAL CUSTOMIZATIONS ===== */
.modal-content {
    border-radius: var(--border-radius-lg);
    border: none;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* ===== ACTIVITY TIMELINE ===== */
.activity-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    color: var(--white);
}

.activity-success {
    background-color: var(--success);
}

.activity-info {
    background-color: var(--info);
}

.activity-warning {
    background-color: var(--warning);
}

.activity-error {
    background-color: var(--error);
}

.activity-content {
    font-size: var(--font-size-sm);
    line-height: 1.4;
}

/* ===== VERIFICATION CHECKLIST ===== */
.verification-checklist {
    background-color: var(--light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
}

.verification-checklist .form-check {
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--medium-gray);
}

.verification-checklist .form-check:last-child {
    border-bottom: none;
}

.verification-checklist .form-check-input:checked {
    background-color: var(--success);
    border-color: var(--success);
}

.verification-checklist .form-check-label {
    font-weight: 500;
    color: var(--text-dark);
}

/* ===== PROGRESS INDICATORS ===== */
.progress {
    background-color: var(--medium-gray);
    border-radius: var(--border-radius-md);
}

.progress-bar {
    background: linear-gradient(90deg, var(--success) 0%, var(--primary-light) 100%);
    border-radius: var(--border-radius-md);
    font-weight: 600;
    font-size: var(--font-size-xs);
}

/* ===== FINANCIAL SUMMARY ===== */
.financial-summary {
    background-color: var(--light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    font-size: var(--font-size-sm);
}

.financial-summary .d-flex {
    align-items: center;
}

/* ===== TIMELINE STYLES ===== */
.timeline {
    position: relative;
    padding-left: var(--spacing-lg);
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--medium-gray);
}

.timeline-item {
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.timeline-marker {
    position: absolute;
    left: -23px;
    top: 5px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid var(--white);
}

.timeline-content {
    background-color: var(--light-gray);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
}

/* ===== DOCUMENT VIEWER ===== */
.document-viewer {
    background-color: var(--light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-xl);
    text-align: center;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

/* ===== COMMENTS SECTION ===== */
.comments-section {
    max-height: 300px;
    overflow-y: auto;
}

.comments-section .border {
    background-color: var(--light-gray);
}

/* ===== STATUS SPECIFIC STYLES ===== */
.status-commissionreview {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info);
    border: 1px solid var(--info);
}

.status-documentreview {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning);
    border: 1px solid var(--warning);
}

.status-verificationcomplete {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
    border: 1px solid var(--success);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .dashboard-main {
        padding: var(--spacing-md);
    }

    .stat-card {
        padding: var(--spacing-md);
        gap: var(--spacing-md);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-xl);
    }

    .case-info {
        grid-template-columns: 1fr;
    }

    .case-actions {
        justify-content: center;
    }

    .dashboard-filters .row {
        justify-content: center;
    }

    .timeline {
        padding-left: var(--spacing-md);
    }

    .timeline-marker {
        left: -19px;
    }

    .verification-checklist {
        padding: var(--spacing-md);
    }

    .financial-summary {
        padding: var(--spacing-md);
    }
}
