/**
 * PLRA E-Stamping and Registration System
 * Sub-Registrar Dashboard JavaScript
 * Version: 1.0
 */

// Sample data for demonstration
const sampleCases = [
    {
        id: 'PLRA-2024-001',
        applicantName: '<PERSON>',
        applicantPhone: '+92-300-1234567',
        applicantEmail: 'muham<PERSON>.<EMAIL>',
        type: 'Agricultural Land Sale',
        category: 'property',
        status: 'pending',
        priority: 'high',
        submissionDate: '2024-01-15',
        stampDuty: 30000,
        registrationFee: 300,
        propertyValue: 1500000,
        propertyLocation: 'Village Chak Jhumra, Tehsil Faisalabad',
        documents: [
            { name: 'Revenue Records (Khasra)', status: 'uploaded', verified: false },
            { name: 'Mutation Certificate', status: 'uploaded', verified: false },
            { name: 'Identity Proof', status: 'uploaded', verified: true },
            { name: 'NOC from Agriculture Dept', status: 'pending', verified: false }
        ],
        comments: [],
        lastAction: 'Document submission completed',
        assignedOfficer: 'Sub-Registrar: <PERSON>'
    },
    {
        id: 'PLRA-2024-002',
        applicantName: 'Fatima Khan',
        applicantPhone: '+92-321-9876543',
        applicantEmail: '<EMAIL>',
        type: 'Commercial Property Transfer',
        category: 'property',
        status: 'in-review',
        priority: 'medium',
        submissionDate: '2024-01-14',
        stampDuty: 100000,
        registrationFee: 800,
        propertyValue: 2000000,
        propertyLocation: 'DHA Phase 5, Lahore',
        documents: [
            { name: 'Property Title Documents', status: 'uploaded', verified: true },
            { name: 'Commercial License', status: 'uploaded', verified: true },
            { name: 'Building Plan Approval', status: 'uploaded', verified: false },
            { name: 'Tax Clearance Certificate', status: 'uploaded', verified: true }
        ],
        comments: [
            { author: 'Sub-Registrar', date: '2024-01-14', text: 'Building plan approval needs verification from municipal authority.' }
        ],
        lastAction: 'Under document verification',
        assignedOfficer: 'Sub-Registrar: Muhammad Tariq'
    },
    {
        id: 'PLRA-2024-003',
        applicantName: 'Ayesha Malik',
        applicantPhone: '+92-333-5678901',
        applicantEmail: '<EMAIL>',
        type: 'Partnership Deed',
        category: 'business',
        status: 'verified',
        priority: 'low',
        submissionDate: '2024-01-13',
        stampDuty: 1100,
        registrationFee: 400,
        propertyValue: null,
        propertyLocation: null,
        documents: [
            { name: 'Partnership Agreement', status: 'uploaded', verified: true },
            { name: 'Identity Proof (All Partners)', status: 'uploaded', verified: true },
            { name: 'Business Plan', status: 'uploaded', verified: true },
            { name: 'NTN Certificates', status: 'uploaded', verified: true }
        ],
        comments: [
            { author: 'Sub-Registrar', date: '2024-01-13', text: 'All documents verified. Ready for registration.' }
        ],
        lastAction: 'Verification completed - Ready for registration',
        assignedOfficer: 'Sub-Registrar: Muhammad Tariq'
    }
];

// Dashboard state
let currentSection = 'overview';
let filteredCases = [...sampleCases];

/**
 * Initialize the dashboard
 */
function initializeDashboard() {
    // Set current date
    document.getElementById('currentDate').textContent = new Date().toLocaleDateString('en-IN', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    // Setup navigation
    setupNavigation();
    
    // Load initial data
    loadOverviewData();
    loadRecentCases();
    
    // Setup filters
    setupFilters();
    
    // Load pending cases
    loadPendingCases();
}

/**
 * Setup navigation between sections
 */
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link[data-section]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));
            
            // Add active class to clicked link
            this.classList.add('active');
            
            // Hide all sections
            document.querySelectorAll('.dashboard-section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Show selected section
            const sectionId = this.getAttribute('data-section') + '-section';
            const section = document.getElementById(sectionId);
            if (section) {
                section.classList.add('active');
                currentSection = this.getAttribute('data-section');
                
                // Load section-specific data
                loadSectionData(currentSection);
            }
        });
    });
}

/**
 * Load data for specific section
 */
function loadSectionData(section) {
    switch(section) {
        case 'pending-cases':
            loadPendingCases();
            break;
        case 'in-review':
            loadInReviewCases();
            break;
        case 'verified':
            loadVerifiedCases();
            break;
        case 'rejected':
            loadRejectedCases();
            break;
        case 'reports':
            loadReports();
            break;
    }
}

/**
 * Load overview statistics
 */
function loadOverviewData() {
    // Statistics are already hardcoded in HTML for demo
    // In real application, this would fetch from API
}

/**
 * Load recent cases for overview
 */
function loadRecentCases() {
    const tableBody = document.getElementById('recentCasesTable');
    const recentCases = sampleCases.slice(0, 5); // Show last 5 cases
    
    tableBody.innerHTML = recentCases.map(caseItem => `
        <tr>
            <td><strong>${caseItem.id}</strong></td>
            <td>${caseItem.applicantName}</td>
            <td>${caseItem.type}</td>
            <td><span class="status-badge status-${caseItem.status}">${formatStatus(caseItem.status)}</span></td>
            <td>${formatDate(caseItem.submissionDate)}</td>
            <td>
                <button class="btn btn-action btn-view btn-sm" onclick="viewCaseDetails('${caseItem.id}')">
                    <i class="fas fa-eye me-1"></i>View
                </button>
            </td>
        </tr>
    `).join('');
}

/**
 * Load pending cases
 */
function loadPendingCases() {
    const pendingCases = filteredCases.filter(c => c.status === 'pending');
    const container = document.getElementById('pendingCasesList');
    
    if (pendingCases.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No pending cases found</h5>
                <p class="text-muted">All cases have been processed or no cases match your filters.</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = pendingCases.map(caseItem => createCaseCard(caseItem)).join('');
}

/**
 * Create case card HTML
 */
function createCaseCard(caseItem) {
    return `
        <div class="case-card">
            <div class="case-header">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <span class="case-id">${caseItem.id}</span>
                    <span class="case-priority priority-${caseItem.priority}">${caseItem.priority.toUpperCase()}</span>
                </div>
            </div>
            
            <div class="case-info">
                <div class="case-info-item">
                    <span class="case-info-label">Applicant</span>
                    <span class="case-info-value">${caseItem.applicantName}</span>
                </div>
                <div class="case-info-item">
                    <span class="case-info-label">Type</span>
                    <span class="case-info-value">${caseItem.type}</span>
                </div>
                <div class="case-info-item">
                    <span class="case-info-label">Status</span>
                    <span class="case-info-value">
                        <span class="status-badge status-${caseItem.status}">${formatStatus(caseItem.status)}</span>
                    </span>
                </div>
                <div class="case-info-item">
                    <span class="case-info-label">Submission Date</span>
                    <span class="case-info-value">${formatDate(caseItem.submissionDate)}</span>
                </div>
                <div class="case-info-item">
                    <span class="case-info-label">Stamp Duty</span>
                    <span class="case-info-value">PKR ${caseItem.stampDuty.toLocaleString('en-PK')}</span>
                </div>
                <div class="case-info-item">
                    <span class="case-info-label">Last Action</span>
                    <span class="case-info-value">${caseItem.lastAction}</span>
                </div>
            </div>
            
            <div class="case-actions">
                <button class="btn btn-action btn-view" onclick="viewCaseDetails('${caseItem.id}')">
                    <i class="fas fa-eye me-1"></i>View Details
                </button>
                <button class="btn btn-action btn-approve" onclick="approveCase('${caseItem.id}')">
                    <i class="fas fa-check me-1"></i>Approve
                </button>
                <button class="btn btn-action btn-forward" onclick="forwardCase('${caseItem.id}')">
                    <i class="fas fa-share me-1"></i>Forward
                </button>
                <button class="btn btn-action btn-reject" onclick="rejectCase('${caseItem.id}')">
                    <i class="fas fa-times me-1"></i>Reject
                </button>
            </div>
        </div>
    `;
}

/**
 * Setup filters
 */
function setupFilters() {
    const categoryFilter = document.getElementById('categoryFilter');
    const dateFilter = document.getElementById('dateFilter');
    const searchFilter = document.getElementById('searchFilter');
    
    if (categoryFilter) {
        categoryFilter.addEventListener('change', applyFilters);
    }
    
    if (dateFilter) {
        dateFilter.addEventListener('change', applyFilters);
    }
    
    if (searchFilter) {
        searchFilter.addEventListener('input', applyFilters);
    }
}

/**
 * Apply filters to cases
 */
function applyFilters() {
    const categoryFilter = document.getElementById('categoryFilter')?.value || '';
    const dateFilter = document.getElementById('dateFilter')?.value || '';
    const searchFilter = document.getElementById('searchFilter')?.value.toLowerCase() || '';
    
    filteredCases = sampleCases.filter(caseItem => {
        const matchesCategory = !categoryFilter || caseItem.category === categoryFilter;
        const matchesDate = !dateFilter || caseItem.submissionDate === dateFilter;
        const matchesSearch = !searchFilter || 
            caseItem.applicantName.toLowerCase().includes(searchFilter) ||
            caseItem.id.toLowerCase().includes(searchFilter) ||
            caseItem.type.toLowerCase().includes(searchFilter);
        
        return matchesCategory && matchesDate && matchesSearch;
    });
    
    // Reload current section with filtered data
    loadSectionData(currentSection);
}

/**
 * View case details
 */
function viewCaseDetails(caseId) {
    const caseItem = sampleCases.find(c => c.id === caseId);
    if (!caseItem) return;
    
    const modalContent = document.getElementById('caseDetailsContent');
    modalContent.innerHTML = createCaseDetailsHTML(caseItem);
    
    const modal = new bootstrap.Modal(document.getElementById('caseDetailsModal'));
    modal.show();
}

/**
 * Create case details HTML
 */
function createCaseDetailsHTML(caseItem) {
    return `
        <div class="row">
            <div class="col-md-8">
                <h6 class="text-primary mb-3">Case Information</h6>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>Case ID:</strong> ${caseItem.id}<br>
                        <strong>Type:</strong> ${caseItem.type}<br>
                        <strong>Category:</strong> ${caseItem.category}<br>
                        <strong>Status:</strong> <span class="status-badge status-${caseItem.status}">${formatStatus(caseItem.status)}</span>
                    </div>
                    <div class="col-md-6">
                        <strong>Priority:</strong> <span class="case-priority priority-${caseItem.priority}">${caseItem.priority.toUpperCase()}</span><br>
                        <strong>Submission Date:</strong> ${formatDate(caseItem.submissionDate)}<br>
                        <strong>Assigned Officer:</strong> ${caseItem.assignedOfficer}
                    </div>
                </div>
                
                <h6 class="text-primary mb-3">Applicant Details</h6>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>Name:</strong> ${caseItem.applicantName}<br>
                        <strong>Phone:</strong> ${caseItem.applicantPhone}<br>
                        <strong>Email:</strong> ${caseItem.applicantEmail}
                    </div>
                    <div class="col-md-6">
                        ${caseItem.propertyLocation ? `<strong>Property Location:</strong> ${caseItem.propertyLocation}<br>` : ''}
                        ${caseItem.propertyValue ? `<strong>Property Value:</strong> PKR ${caseItem.propertyValue.toLocaleString('en-PK')}<br>` : ''}
                    </div>
                </div>
                
                <h6 class="text-primary mb-3">Financial Details</h6>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>Stamp Duty:</strong> PKR ${caseItem.stampDuty.toLocaleString('en-PK')}<br>
                        <strong>Registration Fee:</strong> PKR ${caseItem.registrationFee.toLocaleString('en-PK')}
                    </div>
                    <div class="col-md-6">
                        <strong>Total Amount:</strong> PKR ${(caseItem.stampDuty + caseItem.registrationFee).toLocaleString('en-PK')}
                    </div>
                </div>
                
                <h6 class="text-primary mb-3">Documents</h6>
                <div class="table-responsive mb-3">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Document</th>
                                <th>Status</th>
                                <th>Verified</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${caseItem.documents.map(doc => `
                                <tr>
                                    <td>${doc.name}</td>
                                    <td><span class="badge ${doc.status === 'uploaded' ? 'bg-success' : 'bg-warning'}">${doc.status}</span></td>
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" ${doc.verified ? 'checked' : ''} 
                                                   onchange="toggleDocumentVerification('${caseItem.id}', '${doc.name}', this.checked)">
                                        </div>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewDocument('${doc.name}')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="downloadDocument('${doc.name}')">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                
                <h6 class="text-primary mb-3">Comments & Actions</h6>
                <div class="mb-3">
                    ${caseItem.comments.map(comment => `
                        <div class="border rounded p-2 mb-2">
                            <small class="text-muted">${comment.author} - ${formatDate(comment.date)}</small><br>
                            ${comment.text}
                        </div>
                    `).join('')}
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Add Comment:</label>
                    <textarea class="form-control" rows="3" id="newComment-${caseItem.id}" placeholder="Enter your comments..."></textarea>
                </div>
            </div>
            
            <div class="col-md-4">
                <h6 class="text-primary mb-3">Actions</h6>
                <div class="d-grid gap-2">
                    <button class="btn btn-success" onclick="approveAndForward('${caseItem.id}')">
                        <i class="fas fa-check-circle me-2"></i>Approve & Forward
                    </button>
                    <button class="btn btn-warning" onclick="sendToCommission('${caseItem.id}')">
                        <i class="fas fa-share me-2"></i>Send to Local Commission
                    </button>
                    <button class="btn btn-info" onclick="requestDocuments('${caseItem.id}')">
                        <i class="fas fa-file-plus me-2"></i>Request Additional Documents
                    </button>
                    <button class="btn btn-danger" onclick="rejectWithComments('${caseItem.id}')">
                        <i class="fas fa-times-circle me-2"></i>Reject with Comments
                    </button>
                </div>
                
                <hr>
                
                <h6 class="text-primary mb-3">Case Timeline</h6>
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <small class="text-muted">${formatDate(caseItem.submissionDate)}</small><br>
                            <strong>Case Submitted</strong><br>
                            Application submitted by ${caseItem.applicantName}
                        </div>
                    </div>
                    ${caseItem.comments.map(comment => `
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <small class="text-muted">${formatDate(comment.date)}</small><br>
                                <strong>${comment.author}</strong><br>
                                ${comment.text}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
}

/**
 * Utility functions
 */
function formatStatus(status) {
    const statusMap = {
        'pending': 'Pending',
        'in-review': 'In Review',
        'verified': 'Verified',
        'rejected': 'Rejected'
    };
    return statusMap[status] || status;
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-IN');
}

// Action functions (placeholders for actual implementation)
function approveCase(caseId) {
    alert(`Approving case: ${caseId}`);
}

function forwardCase(caseId) {
    alert(`Forwarding case: ${caseId}`);
}

function rejectCase(caseId) {
    alert(`Rejecting case: ${caseId}`);
}

function toggleDocumentVerification(caseId, docName, verified) {
    console.log(`Document ${docName} for case ${caseId} verification: ${verified}`);
}

function viewDocument(docName) {
    alert(`Viewing document: ${docName}`);
}

function downloadDocument(docName) {
    alert(`Downloading document: ${docName}`);
}

function approveAndForward(caseId) {
    alert(`Approving and forwarding case: ${caseId}`);
}

function sendToCommission(caseId) {
    alert(`Sending case ${caseId} to Local Commission`);
}

function requestDocuments(caseId) {
    alert(`Requesting additional documents for case: ${caseId}`);
}

function rejectWithComments(caseId) {
    const comment = document.getElementById(`newComment-${caseId}`)?.value;
    alert(`Rejecting case ${caseId} with comment: ${comment}`);
}

// Placeholder functions for other sections
function loadInReviewCases() {
    const inReviewCases = filteredCases.filter(c => c.status === 'in-review');
    // Implementation similar to loadPendingCases
}

function loadVerifiedCases() {
    const verifiedCases = filteredCases.filter(c => c.status === 'verified');
    // Implementation similar to loadPendingCases
}

function loadRejectedCases() {
    const rejectedCases = filteredCases.filter(c => c.status === 'rejected');
    // Implementation similar to loadPendingCases
}

function loadReports() {
    // Implementation for reports section
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});
