/* 
 * Punjab Land Record Authority (PLRA) - E-Stamping and Registration System
 * Centralized Theme File
 * Version: 1.0
 */

/* ===== CSS VARIABLES (CUSTOM PROPERTIES) ===== */
:root {
  /* Primary Colors - Modern Teal Theme */
  --primary-color: #0891B2;        /* Modern Teal */
  --primary-light: #06B6D4;        /* Cyan */
  --primary-dark: #0E7490;         /* Dark Teal */

  /* Secondary Colors - Modern Purple */
  --secondary-color: #7C3AED;      /* Modern Purple */
  --secondary-light: #A855F7;      /* Light Purple */
  --secondary-dark: #5B21B6;       /* Dark Purple */
  
  /* Neutral Colors */
  --white: #FFFFFF;
  --light-gray: #F8FAFC;           /* Modern Light Gray */
  --medium-gray: #E2E8F0;          /* Modern Medium Gray */
  --dark-gray: #64748B;            /* Modern Dark Gray */
  --text-dark: #1E293B;            /* Modern Dark Text */
  --text-light: #64748B;           /* Modern Light Text */
  
  /* Status Colors */
  --success: #10B981;              /* Modern Green */
  --warning: #F59E0B;              /* Modern Amber */
  --error: #EF4444;                /* Modern Red */
  --info: #3B82F6;                 /* Modern Blue */
  
  /* Typography */
  --font-family-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family-secondary: 'Arial', sans-serif;
  
  /* Font Sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border Radius */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  
  /* Shadows - Modern and Subtle */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* ===== GLOBAL STYLES ===== */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-dark);
  background-color: var(--light-gray);
  margin: 0;
  padding: 0;
}

/* ===== HEADER STYLES ===== */
.plra-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  padding: var(--spacing-lg) 0;
  box-shadow: var(--shadow-md);
}

.plra-header .navbar-brand {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--white) !important;
}

.plra-header .navbar-brand:hover {
  color: var(--secondary-light) !important;
}

/* ===== MAIN CONTENT STYLES ===== */
.plra-main {
  min-height: calc(100vh - 200px);
  padding: var(--spacing-2xl) 0;
}

.plra-page-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--primary-dark);
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.plra-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-light);
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

/* ===== CARD STYLES ===== */
.plra-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  border: none;
  transition: all 0.3s ease;
  overflow: hidden;
}

.plra-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.plra-card-header {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
  color: var(--white);
  border: none;
  padding: var(--spacing-lg);
  font-weight: 600;
}

.plra-card-body {
  padding: var(--spacing-xl);
}

.plra-card-icon {
  font-size: var(--font-size-4xl);
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

/* ===== BUTTON STYLES ===== */
.btn-plra-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: none;
  color: var(--white);
  padding: var(--spacing-sm) var(--spacing-xl);
  border-radius: var(--border-radius-md);
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-plra-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: var(--white);
}

.btn-plra-secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
  border: none;
  color: var(--white);
  padding: var(--spacing-sm) var(--spacing-xl);
  border-radius: var(--border-radius-md);
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-plra-secondary:hover {
  background: linear-gradient(135deg, var(--secondary-dark) 0%, var(--secondary-color) 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: var(--white);
}

/* ===== SIDEBAR STYLES ===== */
.plra-sidebar {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  height: fit-content;
}

.plra-sidebar-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--primary-light);
}

.plra-sidebar-item {
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.plra-sidebar-item:hover {
  background-color: var(--light-gray);
  border-left-color: var(--primary-color);
}

.plra-sidebar-item.active {
  background-color: var(--primary-light);
  color: var(--white);
  border-left-color: var(--primary-dark);
}

/* ===== DETAIL PANEL STYLES ===== */
.plra-detail-panel {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-xl);
  min-height: 400px;
}

.plra-detail-title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: var(--spacing-lg);
}

.plra-detail-description {
  color: var(--text-light);
  margin-bottom: var(--spacing-xl);
  line-height: 1.8;
}

/* ===== FOOTER STYLES ===== */
.plra-footer {
  background-color: var(--primary-dark);
  color: var(--white);
  padding: var(--spacing-xl) 0;
  margin-top: var(--spacing-2xl);
}

/* ===== UTILITY CLASSES ===== */
.text-plra-primary { color: var(--primary-color) !important; }
.text-plra-secondary { color: var(--secondary-color) !important; }
.bg-plra-primary { background-color: var(--primary-color) !important; }
.bg-plra-secondary { background-color: var(--secondary-color) !important; }

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .plra-page-title {
    font-size: var(--font-size-2xl);
  }
  
  .plra-card-body {
    padding: var(--spacing-lg);
  }
  
  .plra-detail-panel {
    margin-top: var(--spacing-lg);
  }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* ===== BREADCRUMB STYLES ===== */
.plra-breadcrumb {
  background: var(--white);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md) var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.plra-breadcrumb .breadcrumb-item.active {
  color: var(--primary-color);
}
