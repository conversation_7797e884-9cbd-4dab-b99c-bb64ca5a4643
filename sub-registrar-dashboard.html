<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sub-Registrar Dashboard - PLRA E-Stamping & Registration</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom Theme CSS -->
    <link href="assets/css/theme.css" rel="stylesheet">
    
    <!-- Dashboard Specific CSS -->
    <link href="assets/css/dashboard.css" rel="stylesheet">
</head>
<body class="dashboard-body">
    <!-- Header -->
    <header class="plra-header dashboard-header">
        <div class="container-fluid">
            <nav class="navbar navbar-expand-lg">
                <div class="container-fluid">
                    <a class="navbar-brand" href="index.html">
                        <i class="fas fa-landmark me-2"></i>
                        PLRA E-Stamping & Registration
                    </a>
                    
                    <!-- User Info -->
                    <div class="navbar-nav ms-auto">
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle text-white d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle me-2"></i>
                                <span>Sub-Registrar: Rajesh Kumar</span>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="index.html"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Dashboard Content -->
    <div class="container-fluid dashboard-container">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 dashboard-sidebar">
                <div class="sidebar-content">
                    <h6 class="sidebar-title">Dashboard Menu</h6>
                    <ul class="nav nav-pills flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" data-section="overview">
                                <i class="fas fa-tachometer-alt me-2"></i>Overview
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-section="pending-cases">
                                <i class="fas fa-clock me-2"></i>Pending Cases
                                <span class="badge bg-warning ms-auto">12</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-section="in-review">
                                <i class="fas fa-search me-2"></i>In Review
                                <span class="badge bg-info ms-auto">8</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-section="verified">
                                <i class="fas fa-check-circle me-2"></i>Verified
                                <span class="badge bg-success ms-auto">25</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-section="rejected">
                                <i class="fas fa-times-circle me-2"></i>Rejected
                                <span class="badge bg-danger ms-auto">3</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-section="reports">
                                <i class="fas fa-chart-bar me-2"></i>Reports
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-md-9 col-lg-10 dashboard-main">
                <!-- Overview Section -->
                <div id="overview-section" class="dashboard-section active">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="dashboard-title">Sub-Registrar Dashboard</h2>
                        <div class="dashboard-date">
                            <i class="fas fa-calendar me-2"></i>
                            <span id="currentDate"></span>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stat-card stat-card-primary">
                                <div class="stat-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="stat-content">
                                    <h3>48</h3>
                                    <p>Total Cases</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stat-card stat-card-warning">
                                <div class="stat-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stat-content">
                                    <h3>12</h3>
                                    <p>Pending Review</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stat-card stat-card-success">
                                <div class="stat-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stat-content">
                                    <h3>25</h3>
                                    <p>Verified Today</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stat-card stat-card-info">
                                <div class="stat-icon">
                                    <i class="fas fa-rupee-sign"></i>
                                </div>
                                <div class="stat-content">
                                    <h3>₹2.4L</h3>
                                    <p>Revenue Today</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Cases Table -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>Recent Cases</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Case ID</th>
                                            <th>Applicant</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recentCasesTable">
                                        <!-- Cases will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pending Cases Section -->
                <div id="pending-cases-section" class="dashboard-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="dashboard-title">Pending Cases</h2>
                        <div class="dashboard-filters">
                            <div class="row g-2">
                                <div class="col-auto">
                                    <select class="form-select form-select-sm" id="categoryFilter">
                                        <option value="">All Categories</option>
                                        <option value="property">Property</option>
                                        <option value="business">Business</option>
                                        <option value="legal">Legal</option>
                                    </select>
                                </div>
                                <div class="col-auto">
                                    <input type="date" class="form-control form-control-sm" id="dateFilter">
                                </div>
                                <div class="col-auto">
                                    <input type="text" class="form-control form-control-sm" placeholder="Search..." id="searchFilter">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-body">
                            <div id="pendingCasesList">
                                <!-- Pending cases will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Case Details Modal will be added here -->
            </div>
        </div>
    </div>

    <!-- Case Details Modal -->
    <div class="modal fade" id="caseDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Case Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="caseDetailsContent">
                    <!-- Case details will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/sub-registrar-dashboard.js"></script>
</body>
</html>
