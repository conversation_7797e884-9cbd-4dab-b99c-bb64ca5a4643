/**
 * PLRA E-Stamping and Registration System
 * Categories Page JavaScript
 * Version: 1.0
 */

// Current selections
let activeCategoryId = '';
let selectedSubcategory = '';

/**
 * Initialize categories page
 */
function initializeCategoriesPage() {
    // Get category from URL parameter
    activeCategoryId = window.PLRA.getUrlParameter('category') || 'property';

    // Validate category exists
    if (!window.PLRA.categoryData[activeCategoryId]) {
        document.getElementById('detailContent').innerHTML = window.PLRA.showError('Invalid category selected.');
        return;
    }

    // Update page title and breadcrumb
    updatePageHeader();

    // Load subcategories
    loadSubcategories();
}

/**
 * Update page header with category information
 */
function updatePageHeader() {
    const categoryInfo = window.PLRA.categoryData[activeCategoryId];

    // Update page title
    document.getElementById('categoryTitle').innerHTML = `
        <i class="${categoryInfo.icon} me-3"></i>${categoryInfo.title}
    `;

    // Update breadcrumb
    document.getElementById('categoryBreadcrumb').textContent = categoryInfo.title;
}

/**
 * Load and display subcategories
 */
function loadSubcategories() {
    const subcategoryList = document.getElementById('subcategoryList');
    const categoryInfo = window.PLRA.categoryData[activeCategoryId];

    // Clear existing content
    subcategoryList.innerHTML = '';

    // Create subcategory items
    Object.keys(categoryInfo.subcategories).forEach((subcategoryId, index) => {
        const subcategory = categoryInfo.subcategories[subcategoryId];
        
        const subcategoryItem = document.createElement('div');
        subcategoryItem.className = 'plra-sidebar-item';
        subcategoryItem.setAttribute('data-subcategory', subcategoryId);
        
        subcategoryItem.innerHTML = `
            <h6 class="mb-1">${subcategory.title}</h6>
            <small class="text-muted">${subcategory.description.substring(0, 60)}...</small>
        `;
        
        // Add click event
        subcategoryItem.addEventListener('click', () => selectSubcategory(subcategoryId));
        
        subcategoryList.appendChild(subcategoryItem);
        
        // Auto-select first item
        if (index === 0) {
            setTimeout(() => selectSubcategory(subcategoryId), 300);
        }
    });
}

/**
 * Select and display subcategory details
 * @param {string} subcategoryId - Subcategory identifier
 */
function selectSubcategory(subcategoryId) {
    selectedSubcategory = subcategoryId;
    
    // Update sidebar selection
    document.querySelectorAll('.plra-sidebar-item').forEach(item => {
        item.classList.remove('active');
    });
    
    const selectedItem = document.querySelector(`[data-subcategory="${subcategoryId}"]`);
    if (selectedItem) {
        selectedItem.classList.add('active');
    }
    
    // Show loading
    document.getElementById('detailContent').innerHTML = window.PLRA.showLoading();
    
    // Simulate loading delay for better UX
    setTimeout(() => {
        displaySubcategoryDetails(subcategoryId);
    }, 500);
}

/**
 * Display detailed information for selected subcategory
 * @param {string} subcategoryId - Subcategory identifier
 */
function displaySubcategoryDetails(subcategoryId) {
    const categoryInfo = window.PLRA.categoryData[activeCategoryId];
    const subcategory = categoryInfo.subcategories[subcategoryId];
    
    if (!subcategory) {
        document.getElementById('detailContent').innerHTML = window.PLRA.showError('Subcategory not found.');
        return;
    }
    
    const detailContent = `
        <div class="fade-in-up">
            <div class="d-flex align-items-center mb-4">
                <i class="${categoryInfo.icon} fa-2x text-plra-primary me-3"></i>
                <div>
                    <h2 class="plra-detail-title mb-1">${subcategory.title}</h2>
                    <p class="text-muted mb-0">${categoryInfo.title}</p>
                </div>
            </div>
            
            <div class="plra-detail-description">
                <p class="lead">${subcategory.description}</p>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card border-0 bg-light">
                        <div class="card-body">
                            <h6 class="text-plra-primary mb-2">
                                <i class="fas fa-money-bill-wave me-2"></i>Stamp Duty
                            </h6>
                            <p class="mb-0 fw-bold">${subcategory.stampDuty}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 bg-light">
                        <div class="card-body">
                            <h6 class="text-plra-primary mb-2">
                                <i class="fas fa-clock me-2"></i>Processing Time
                            </h6>
                            <p class="mb-0 fw-bold">${subcategory.estimatedTime}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mb-4">
                <h5 class="text-plra-primary mb-3">
                    <i class="fas fa-file-alt me-2"></i>Required Documents
                </h5>
                <ul class="list-unstyled">
                    ${window.PLRA.formatDocumentList(subcategory.documents)}
                </ul>
            </div>
            
            <div class="mb-4">
                <h5 class="text-plra-primary mb-3">
                    <i class="fas fa-info-circle me-2"></i>Process Overview
                </h5>
                <div class="row">
                    <div class="col-md-3 text-center mb-3">
                        <div class="bg-plra-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <span class="fw-bold">1</span>
                        </div>
                        <p class="mt-2 small">Document Upload</p>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="bg-plra-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <span class="fw-bold">2</span>
                        </div>
                        <p class="mt-2 small">Verification</p>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="bg-plra-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <span class="fw-bold">3</span>
                        </div>
                        <p class="mt-2 small">Payment</p>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="bg-plra-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <span class="fw-bold">4</span>
                        </div>
                        <p class="mt-2 small">Registration</p>
                    </div>
                </div>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                <button class="btn btn-plra-primary btn-lg me-md-2" onclick="window.PLRA.startWorkflow('${subcategoryId}')">
                    <i class="fas fa-play me-2"></i>Start Registration Process
                </button>
                <button class="btn btn-outline-secondary btn-lg" onclick="downloadInfo('${subcategoryId}')">
                    <i class="fas fa-download me-2"></i>Download Info
                </button>
            </div>
            
            <div class="alert alert-info mt-4" role="alert">
                <i class="fas fa-lightbulb me-2"></i>
                <strong>Tip:</strong> Make sure all documents are clear, legible, and in the required format before starting the process.
            </div>
        </div>
    `;
    
    document.getElementById('detailContent').innerHTML = detailContent;
}

/**
 * Download information about the selected subcategory
 * @param {string} subcategoryId - Subcategory identifier
 */
function downloadInfo(subcategoryId) {
    const categoryInfo = window.PLRA.categoryData[activeCategoryId];
    const subcategory = categoryInfo.subcategories[subcategoryId];
    
    // Create downloadable content
    const content = `
PLRA E-Stamping and Registration System
${categoryInfo.title} - ${subcategory.title}

Description: ${subcategory.description}

Stamp Duty: ${subcategory.stampDuty}
Processing Time: ${subcategory.estimatedTime}
Fees: ${subcategory.fees}

Required Documents:
${subcategory.documents.map(doc => `• ${doc}`).join('\n')}

Process Steps:
1. Document Upload - Prepare and upload all required documents
2. Verification - Documents will be verified by authorities
3. Payment - Pay the required stamp duty and fees
4. Registration - Complete the registration process

For more information, visit: https://plra.punjab.gov.in
Helpline: 1800-XXX-XXXX

Generated on: ${new Date().toLocaleDateString()}
    `;
    
    // Create and download file
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `PLRA_${subcategory.title.replace(/\s+/g, '_')}_Info.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    // Show success message
    const alert = document.createElement('div');
    alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        Information downloaded successfully!
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alert);
    
    // Auto-remove alert after 3 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 3000);
}

// Initialize page when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing categories page...');
    initializeCategoriesPage();
});

// Also try to initialize after a short delay in case DOMContentLoaded already fired
setTimeout(function() {
    if (document.readyState === 'complete') {
        console.log('Document ready, initializing categories page...');
        initializeCategoriesPage();
    }
}, 100);

// Export functions
window.downloadInfo = downloadInfo;
