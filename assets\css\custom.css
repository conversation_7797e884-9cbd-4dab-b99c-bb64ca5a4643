/* 
 * PLRA E-Stamping and Registration System
 * Custom CSS for additional styling
 * Version: 1.0
 */

/* Additional custom styles can be added here */
/* This file is for project-specific customizations that don't belong in the main theme */

/* Example: Custom animations */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* Example: Custom hover effects */
.custom-hover-effect:hover {
  transform: scale(1.02);
  transition: transform 0.2s ease;
}

/* Example: Custom loading spinner */
.custom-spinner {
  border: 4px solid var(--light-gray);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Custom alert styles */
.alert-plra {
  border-left: 4px solid var(--primary-color);
  background-color: rgba(46, 125, 50, 0.1);
  border-color: var(--primary-light);
}

/* Custom form styles */
.form-control-plra {
  border: 2px solid var(--medium-gray);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  transition: border-color 0.3s ease;
}

.form-control-plra:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
}

/* Custom table styles */
.table-plra {
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table-plra thead th {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  font-weight: 600;
}

.table-plra tbody tr:hover {
  background-color: var(--light-gray);
}

/* Custom progress bar */
.progress-plra {
  height: 8px;
  border-radius: var(--border-radius-md);
  background-color: var(--light-gray);
}

.progress-plra .progress-bar {
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 100%);
  border-radius: var(--border-radius-md);
}

/* Custom badge styles */
.badge-plra-success {
  background-color: var(--success);
  color: var(--white);
}

.badge-plra-warning {
  background-color: var(--warning);
  color: var(--white);
}

.badge-plra-error {
  background-color: var(--error);
  color: var(--white);
}

/* Custom modal styles */
.modal-plra .modal-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  border: none;
}

.modal-plra .modal-header .btn-close {
  filter: invert(1);
}

/* Custom dropdown styles */
.dropdown-plra .dropdown-menu {
  border: none;
  box-shadow: var(--shadow-lg);
  border-radius: var(--border-radius-md);
}

.dropdown-plra .dropdown-item:hover {
  background-color: var(--primary-light);
  color: var(--white);
}

/* Print styles */
@media print {
  .plra-header,
  .plra-footer,
  .btn,
  .plra-sidebar {
    display: none !important;
  }
  
  .plra-detail-panel {
    box-shadow: none !important;
    border: 1px solid var(--medium-gray) !important;
  }
  
  body {
    background-color: var(--white) !important;
  }
}
