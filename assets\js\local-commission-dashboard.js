/**
 * PLRA E-Stamping and Registration System
 * Local Commission Dashboard JavaScript
 * Version: 1.0
 */

// Sample data for Local Commission cases (forwarded from Sub-Registrars)
const commissionCases = [
    {
        id: 'PLRA-2024-004',
        applicantName: '<PERSON><PERSON><PERSON><PERSON>',
        applicantPhone: '+91-98765-43211',
        applicantEmail: '<EMAIL>',
        type: 'Residential Property Sale',
        category: 'property',
        status: 'commission-review',
        priority: 'high',
        submissionDate: '2024-01-12',
        forwardedDate: '2024-01-14',
        forwardedBy: 'Sub-Registrar: <PERSON><PERSON>',
        stampDuty: 75000,
        registrationFee: 750,
        propertyValue: 2500000,
        propertyLocation: 'Sector 15, Chandigarh',
        documents: [
            { name: 'Property Title Documents', status: 'uploaded', verified: true, commissionReviewed: false },
            { name: 'Building Plan Approval', status: 'uploaded', verified: true, commissionReviewed: false },
            { name: 'Identity Proof', status: 'uploaded', verified: true, commissionReviewed: true },
            { name: 'Property Tax Receipts', status: 'uploaded', verified: true, commissionReviewed: false }
        ],
        verificationChecklist: {
            propertyVerification: false,
            legalCompliance: false,
            documentCompleteness: false,
            stampDutyCalculation: false,
            applicantVerification: true
        },
        subRegistrarComments: [
            { author: 'Sub-Registrar: Rajesh Kumar', date: '2024-01-14', text: 'All documents verified. Property requires commission review for final approval.' }
        ],
        commissionComments: [],
        lastAction: 'Forwarded to Local Commission for review',
        assignedOfficer: 'Commission Member: Dr. Manjit Kaur'
    },
    {
        id: 'PLRA-2024-005',
        applicantName: 'Simran Kaur',
        applicantPhone: '+91-87654-32110',
        applicantEmail: '<EMAIL>',
        type: 'Joint Venture Agreement',
        category: 'business',
        status: 'document-review',
        priority: 'medium',
        submissionDate: '2024-01-11',
        forwardedDate: '2024-01-13',
        forwardedBy: 'Sub-Registrar: Rajesh Kumar',
        stampDuty: 25000,
        registrationFee: 1000,
        propertyValue: null,
        propertyLocation: null,
        documents: [
            { name: 'JV Agreement', status: 'uploaded', verified: true, commissionReviewed: true },
            { name: 'Company Registration Certificates', status: 'uploaded', verified: true, commissionReviewed: true },
            { name: 'Board Resolutions', status: 'uploaded', verified: true, commissionReviewed: false },
            { name: 'Financial Statements', status: 'uploaded', verified: true, commissionReviewed: false }
        ],
        verificationChecklist: {
            propertyVerification: true, // N/A for business
            legalCompliance: false,
            documentCompleteness: false,
            stampDutyCalculation: true,
            applicantVerification: true
        },
        subRegistrarComments: [
            { author: 'Sub-Registrar: Rajesh Kumar', date: '2024-01-13', text: 'Business documents verified. Requires commission review for legal compliance.' }
        ],
        commissionComments: [
            { author: 'Commission Member: Dr. Manjit Kaur', date: '2024-01-15', text: 'Reviewing board resolutions and financial statements.' }
        ],
        lastAction: 'Under commission document review',
        assignedOfficer: 'Commission Member: Dr. Manjit Kaur'
    },
    {
        id: 'PLRA-2024-006',
        applicantName: 'Ravi Sharma',
        applicantPhone: '+91-76543-21099',
        applicantEmail: '<EMAIL>',
        type: 'Property Power of Attorney',
        category: 'legal',
        status: 'verification-complete',
        priority: 'low',
        submissionDate: '2024-01-10',
        forwardedDate: '2024-01-12',
        forwardedBy: 'Sub-Registrar: Rajesh Kumar',
        stampDuty: 500,
        registrationFee: 300,
        propertyValue: 1800000,
        propertyLocation: 'Village Mohali, Punjab',
        documents: [
            { name: 'Property Documents', status: 'uploaded', verified: true, commissionReviewed: true },
            { name: 'Identity Proof (Both Parties)', status: 'uploaded', verified: true, commissionReviewed: true },
            { name: 'Witness Statements', status: 'uploaded', verified: true, commissionReviewed: true },
            { name: 'Property Valuation', status: 'uploaded', verified: true, commissionReviewed: true }
        ],
        verificationChecklist: {
            propertyVerification: true,
            legalCompliance: true,
            documentCompleteness: true,
            stampDutyCalculation: true,
            applicantVerification: true
        },
        subRegistrarComments: [
            { author: 'Sub-Registrar: Rajesh Kumar', date: '2024-01-12', text: 'All documents verified. Ready for commission approval.' }
        ],
        commissionComments: [
            { author: 'Commission Member: Dr. Manjit Kaur', date: '2024-01-15', text: 'Verification completed. Ready for final registration.' }
        ],
        lastAction: 'Verification completed - Ready for registration',
        assignedOfficer: 'Commission Member: Dr. Manjit Kaur'
    }
];

// Recent activity data
const recentActivity = [
    { time: '10:30 AM', action: 'Approved case PLRA-2024-006 for final registration', type: 'success' },
    { time: '09:45 AM', action: 'Started document review for case PLRA-2024-005', type: 'info' },
    { time: '09:15 AM', action: 'Received new case PLRA-2024-004 from Sub-Registrar', type: 'warning' },
    { time: '08:30 AM', action: 'Generated registration certificate for PLRA-2024-003', type: 'success' },
    { time: '08:00 AM', action: 'Logged into commission dashboard', type: 'info' }
];

// Dashboard state
let currentSection = 'overview';
let filteredCommissionCases = [...commissionCases];

/**
 * Initialize the Local Commission dashboard
 */
function initializeCommissionDashboard() {
    // Set current date
    document.getElementById('currentDate').textContent = new Date().toLocaleDateString('en-IN', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    // Setup navigation
    setupCommissionNavigation();

    // Load initial data
    loadCommissionOverviewData();
    loadRecentActivity();

    // Setup filters
    setupCommissionFilters();

    // Load assigned cases
    loadAssignedCases();
}

/**
 * Setup navigation between sections
 */
function setupCommissionNavigation() {
    const navLinks = document.querySelectorAll('.nav-link[data-section]');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));

            // Add active class to clicked link
            this.classList.add('active');

            // Hide all sections
            document.querySelectorAll('.dashboard-section').forEach(section => {
                section.classList.remove('active');
            });

            // Show selected section
            const sectionId = this.getAttribute('data-section') + '-section';
            const section = document.getElementById(sectionId);
            if (section) {
                section.classList.add('active');
                currentSection = this.getAttribute('data-section');

                // Load section-specific data
                loadCommissionSectionData(currentSection);
            }
        });
    });
}

/**
 * Load data for specific section
 */
function loadCommissionSectionData(section) {
    switch(section) {
        case 'assigned-cases':
            loadAssignedCases();
            break;
        case 'document-review':
            loadDocumentReviewCases();
            break;
        case 'verification':
            loadVerificationCases();
            break;
        case 'approved':
            loadApprovedCases();
            break;
        case 'audit-trail':
            loadAuditTrail();
            break;
        case 'reports':
            loadCommissionReports();
            break;
    }
}

/**
 * Load overview statistics
 */
function loadCommissionOverviewData() {
    // Statistics are already hardcoded in HTML for demo
    // In real application, this would fetch from API
}

/**
 * Load recent activity
 */
function loadRecentActivity() {
    const container = document.getElementById('recentActivity');

    container.innerHTML = recentActivity.map(activity => `
        <div class="d-flex align-items-center mb-3">
            <div class="flex-shrink-0">
                <div class="activity-icon activity-${activity.type}">
                    <i class="fas fa-${getActivityIcon(activity.type)}"></i>
                </div>
            </div>
            <div class="flex-grow-1 ms-3">
                <div class="activity-content">
                    <small class="text-muted">${activity.time}</small><br>
                    ${activity.action}
                </div>
            </div>
        </div>
    `).join('');
}

/**
 * Get activity icon based on type
 */
function getActivityIcon(type) {
    const icons = {
        'success': 'check-circle',
        'info': 'info-circle',
        'warning': 'exclamation-triangle',
        'error': 'times-circle'
    };
    return icons[type] || 'circle';
}

/**
 * Load assigned cases
 */
function loadAssignedCases() {
    const assignedCases = filteredCommissionCases.filter(c =>
        c.status === 'commission-review' || c.status === 'document-review' || c.status === 'verification-complete'
    );
    const container = document.getElementById('assignedCasesList');

    if (assignedCases.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No assigned cases found</h5>
                <p class="text-muted">All cases have been processed or no cases match your filters.</p>
            </div>
        `;
        return;
    }

    container.innerHTML = assignedCases.map(caseItem => createCommissionCaseCard(caseItem)).join('');
}

/**
 * Create commission case card HTML
 */
function createCommissionCaseCard(caseItem) {
    const completedChecks = Object.values(caseItem.verificationChecklist).filter(Boolean).length;
    const totalChecks = Object.keys(caseItem.verificationChecklist).length;
    const progressPercentage = (completedChecks / totalChecks) * 100;

    return `
        <div class="case-card">
            <div class="case-header">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <span class="case-id">${caseItem.id}</span>
                    <div class="d-flex align-items-center gap-2">
                        <span class="case-priority priority-${caseItem.priority}">${caseItem.priority.toUpperCase()}</span>
                        <span class="status-badge status-${caseItem.status.replace('-', '')}">${formatCommissionStatus(caseItem.status)}</span>
                    </div>
                </div>
            </div>

            <div class="case-info">
                <div class="case-info-item">
                    <span class="case-info-label">Applicant</span>
                    <span class="case-info-value">${caseItem.applicantName}</span>
                </div>
                <div class="case-info-item">
                    <span class="case-info-label">Type</span>
                    <span class="case-info-value">${caseItem.type}</span>
                </div>
                <div class="case-info-item">
                    <span class="case-info-label">Forwarded By</span>
                    <span class="case-info-value">${caseItem.forwardedBy}</span>
                </div>
                <div class="case-info-item">
                    <span class="case-info-label">Forwarded Date</span>
                    <span class="case-info-value">${formatDate(caseItem.forwardedDate)}</span>
                </div>
                <div class="case-info-item">
                    <span class="case-info-label">Stamp Duty</span>
                    <span class="case-info-value">₹${caseItem.stampDuty.toLocaleString('en-IN')}</span>
                </div>
                <div class="case-info-item">
                    <span class="case-info-label">Verification Progress</span>
                    <span class="case-info-value">
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar bg-success" style="width: ${progressPercentage}%">
                                ${completedChecks}/${totalChecks}
                            </div>
                        </div>
                    </span>
                </div>
            </div>

            <div class="case-actions">
                <button class="btn btn-action btn-view" onclick="reviewCase('${caseItem.id}')">
                    <i class="fas fa-search me-1"></i>Review Case
                </button>
                <button class="btn btn-action btn-approve" onclick="approveForRegistration('${caseItem.id}')">
                    <i class="fas fa-check-double me-1"></i>Approve for Registration
                </button>
                <button class="btn btn-action btn-forward" onclick="returnToSubRegistrar('${caseItem.id}')">
                    <i class="fas fa-undo me-1"></i>Return to Sub-Registrar
                </button>
                <button class="btn btn-action btn-reject" onclick="rejectApplication('${caseItem.id}')">
                    <i class="fas fa-times me-1"></i>Reject Application
                </button>
            </div>
        </div>
    `;
}

/**
 * Setup filters for commission dashboard
 */
function setupCommissionFilters() {
    const priorityFilter = document.getElementById('priorityFilter');
    const typeFilter = document.getElementById('typeFilter');
    const searchFilter = document.getElementById('commissionSearchFilter');

    if (priorityFilter) {
        priorityFilter.addEventListener('change', applyCommissionFilters);
    }

    if (typeFilter) {
        typeFilter.addEventListener('change', applyCommissionFilters);
    }

    if (searchFilter) {
        searchFilter.addEventListener('input', applyCommissionFilters);
    }
}

/**
 * Apply filters to commission cases
 */
function applyCommissionFilters() {
    const priorityFilter = document.getElementById('priorityFilter')?.value || '';
    const typeFilter = document.getElementById('typeFilter')?.value || '';
    const searchFilter = document.getElementById('commissionSearchFilter')?.value.toLowerCase() || '';

    filteredCommissionCases = commissionCases.filter(caseItem => {
        const matchesPriority = !priorityFilter || caseItem.priority === priorityFilter;
        const matchesType = !typeFilter || caseItem.category === typeFilter;
        const matchesSearch = !searchFilter ||
            caseItem.applicantName.toLowerCase().includes(searchFilter) ||
            caseItem.id.toLowerCase().includes(searchFilter) ||
            caseItem.type.toLowerCase().includes(searchFilter);

        return matchesPriority && matchesType && matchesSearch;
    });

    // Reload current section with filtered data
    loadCommissionSectionData(currentSection);
}

/**
 * Review case details
 */
function reviewCase(caseId) {
    const caseItem = commissionCases.find(c => c.id === caseId);
    if (!caseItem) return;

    const modalContent = document.getElementById('caseReviewContent');
    modalContent.innerHTML = createCaseReviewHTML(caseItem);

    const modal = new bootstrap.Modal(document.getElementById('caseReviewModal'));
    modal.show();
}

/**
 * Create case review HTML
 */
function createCaseReviewHTML(caseItem) {
    return `
        <div class="row">
            <div class="col-md-8">
                <h6 class="text-primary mb-3">Case Information</h6>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>Case ID:</strong> ${caseItem.id}<br>
                        <strong>Type:</strong> ${caseItem.type}<br>
                        <strong>Category:</strong> ${caseItem.category}<br>
                        <strong>Status:</strong> <span class="status-badge status-${caseItem.status.replace('-', '')}">${formatCommissionStatus(caseItem.status)}</span>
                    </div>
                    <div class="col-md-6">
                        <strong>Priority:</strong> <span class="case-priority priority-${caseItem.priority}">${caseItem.priority.toUpperCase()}</span><br>
                        <strong>Forwarded Date:</strong> ${formatDate(caseItem.forwardedDate)}<br>
                        <strong>Forwarded By:</strong> ${caseItem.forwardedBy}
                    </div>
                </div>

                <h6 class="text-primary mb-3">Verification Checklist</h6>
                <div class="verification-checklist mb-4">
                    ${Object.entries(caseItem.verificationChecklist).map(([key, value]) => `
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" ${value ? 'checked' : ''}
                                   id="check-${key}" onchange="updateVerificationStatus('${caseItem.id}', '${key}', this.checked)">
                            <label class="form-check-label" for="check-${key}">
                                ${formatChecklistItem(key)}
                            </label>
                        </div>
                    `).join('')}
                </div>

                <h6 class="text-primary mb-3">Documents Review</h6>
                <div class="table-responsive mb-3">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Document</th>
                                <th>Sub-Registrar Verified</th>
                                <th>Commission Reviewed</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${caseItem.documents.map(doc => `
                                <tr>
                                    <td>${doc.name}</td>
                                    <td><span class="badge ${doc.verified ? 'bg-success' : 'bg-warning'}">${doc.verified ? 'Verified' : 'Pending'}</span></td>
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" ${doc.commissionReviewed ? 'checked' : ''}
                                                   onchange="updateDocumentReview('${caseItem.id}', '${doc.name}', this.checked)">
                                        </div>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary me-1" onclick="viewDocument('${doc.name}')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="downloadDocument('${doc.name}')">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>

                <h6 class="text-primary mb-3">Comments History</h6>
                <div class="comments-section mb-3">
                    ${[...caseItem.subRegistrarComments, ...caseItem.commissionComments].map(comment => `
                        <div class="border rounded p-2 mb-2">
                            <small class="text-muted">${comment.author} - ${formatDate(comment.date)}</small><br>
                            ${comment.text}
                        </div>
                    `).join('')}
                </div>

                <div class="mb-3">
                    <label class="form-label">Add Commission Comment:</label>
                    <textarea class="form-control" rows="3" id="newCommissionComment-${caseItem.id}" placeholder="Enter your comments..."></textarea>
                </div>
            </div>

            <div class="col-md-4">
                <h6 class="text-primary mb-3">Commission Actions</h6>
                <div class="d-grid gap-2 mb-4">
                    <button class="btn btn-success" onclick="approveForRegistration('${caseItem.id}')">
                        <i class="fas fa-check-double me-2"></i>Approve for Registration
                    </button>
                    <button class="btn btn-warning" onclick="returnToSubRegistrarWithComments('${caseItem.id}')">
                        <i class="fas fa-undo me-2"></i>Return to Sub-Registrar
                    </button>
                    <button class="btn btn-danger" onclick="rejectApplicationWithComments('${caseItem.id}')">
                        <i class="fas fa-times-circle me-2"></i>Reject Application
                    </button>
                    <button class="btn btn-info" onclick="generateRegistrationCertificate('${caseItem.id}')">
                        <i class="fas fa-certificate me-2"></i>Generate Certificate
                    </button>
                </div>

                <h6 class="text-primary mb-3">Case Progress</h6>
                <div class="progress mb-3" style="height: 25px;">
                    <div class="progress-bar bg-success" style="width: ${calculateCaseProgress(caseItem)}%">
                        ${calculateCaseProgress(caseItem)}%
                    </div>
                </div>

                <h6 class="text-primary mb-3">Financial Summary</h6>
                <div class="financial-summary">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Stamp Duty:</span>
                        <strong>₹${caseItem.stampDuty.toLocaleString('en-IN')}</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Registration Fee:</span>
                        <strong>₹${caseItem.registrationFee.toLocaleString('en-IN')}</strong>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between">
                        <span><strong>Total:</strong></span>
                        <strong>₹${(caseItem.stampDuty + caseItem.registrationFee).toLocaleString('en-IN')}</strong>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Utility functions
 */
function formatCommissionStatus(status) {
    const statusMap = {
        'commission-review': 'Commission Review',
        'document-review': 'Document Review',
        'verification-complete': 'Verification Complete',
        'approved': 'Approved',
        'rejected': 'Rejected'
    };
    return statusMap[status] || status;
}

function formatChecklistItem(key) {
    const itemMap = {
        'propertyVerification': 'Property Verification',
        'legalCompliance': 'Legal Compliance Check',
        'documentCompleteness': 'Document Completeness',
        'stampDutyCalculation': 'Stamp Duty Calculation',
        'applicantVerification': 'Applicant Verification'
    };
    return itemMap[key] || key;
}

function calculateCaseProgress(caseItem) {
    const completedChecks = Object.values(caseItem.verificationChecklist).filter(Boolean).length;
    const totalChecks = Object.keys(caseItem.verificationChecklist).length;
    const reviewedDocs = caseItem.documents.filter(doc => doc.commissionReviewed).length;
    const totalDocs = caseItem.documents.length;

    const checklistProgress = (completedChecks / totalChecks) * 50;
    const documentProgress = (reviewedDocs / totalDocs) * 50;

    return Math.round(checklistProgress + documentProgress);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-IN');
}

// Action functions (placeholders for actual implementation)
function approveForRegistration(caseId) {
    alert(`Approving case ${caseId} for final registration`);
}

function returnToSubRegistrar(caseId) {
    alert(`Returning case ${caseId} to Sub-Registrar`);
}

function rejectApplication(caseId) {
    alert(`Rejecting application ${caseId}`);
}

function updateVerificationStatus(caseId, checkKey, checked) {
    console.log(`Updating verification ${checkKey} for case ${caseId}: ${checked}`);
}

function updateDocumentReview(caseId, docName, reviewed) {
    console.log(`Document ${docName} for case ${caseId} reviewed: ${reviewed}`);
}

function viewDocument(docName) {
    // Show document viewer modal
    const modalContent = document.getElementById('documentViewerContent');
    modalContent.innerHTML = `
        <div class="text-center py-5">
            <i class="fas fa-file-alt fa-3x text-primary mb-3"></i>
            <h5>Document: ${docName}</h5>
            <p class="text-muted">Document viewer would display the actual document here.</p>
            <div class="mt-3">
                <button class="btn btn-primary me-2">
                    <i class="fas fa-search-plus me-1"></i>Zoom In
                </button>
                <button class="btn btn-primary me-2">
                    <i class="fas fa-search-minus me-1"></i>Zoom Out
                </button>
                <button class="btn btn-secondary">
                    <i class="fas fa-download me-1"></i>Download
                </button>
            </div>
        </div>
    `;

    const modal = new bootstrap.Modal(document.getElementById('documentViewerModal'));
    modal.show();
}

function downloadDocument(docName) {
    alert(`Downloading document: ${docName}`);
}

function generateRegistrationCertificate(caseId) {
    alert(`Generating registration certificate for case: ${caseId}`);
}

function returnToSubRegistrarWithComments(caseId) {
    const comment = document.getElementById(`newCommissionComment-${caseId}`)?.value;
    alert(`Returning case ${caseId} to Sub-Registrar with comment: ${comment}`);
}

function rejectApplicationWithComments(caseId) {
    const comment = document.getElementById(`newCommissionComment-${caseId}`)?.value;
    alert(`Rejecting case ${caseId} with comment: ${comment}`);
}

function generateCertificates() {
    alert('Generating certificates for approved cases...');
}

function showSection(sectionName) {
    // Trigger navigation to specific section
    const navLink = document.querySelector(`[data-section="${sectionName}"]`);
    if (navLink) {
        navLink.click();
    }
}

// Placeholder functions for other sections
function loadDocumentReviewCases() {
    const documentReviewCases = filteredCommissionCases.filter(c => c.status === 'document-review');
    // Implementation similar to loadAssignedCases
}

function loadVerificationCases() {
    const verificationCases = filteredCommissionCases.filter(c => c.status === 'commission-review');
    // Implementation similar to loadAssignedCases
}

function loadApprovedCases() {
    const approvedCases = filteredCommissionCases.filter(c => c.status === 'verification-complete');
    // Implementation similar to loadAssignedCases
}

function loadAuditTrail() {
    // Implementation for audit trail section
}

function loadCommissionReports() {
    // Implementation for reports section
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeCommissionDashboard();
});